import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
export function getImgWH(file: UploadFileInfo) {
	return new Promise((resolve, reject) => {
		let _URL = window.URL || window.webkitURL;
		var img = new Image();
		img.src = _URL.createObjectURL(file.file);
		img.onload = function () {
			resolve(img);
		};
	});
}
export function getLabelByOptions(
	value: string | number,
	options: Common.OptionWithKey<any>[]
) {
	// console.log("getLabelByOptions", value, options);
	if (options.length > 0) {
		return options.find((item) => item.value == value)?.label || "--";
	}
	return "--";
}
/**
 * 获取数组笛卡尔 结果积
 * @param arrays 二维数组 [[1,2],[a,b]]
 * @returns [[1,a],[1,b],[2,a],[2,b]]
 */
export function cartesianFn(arrays: any[]) {
	return arrays.reduce(
		function (a, b) {
			return a
				.map(function (x) {
					return b.map(function (y) {
						return x.concat(y);
					});
				})
				.reduce(function (a, b) {
					return a.concat(b);
				}, []);
		},
		[[]]
	);
}
export const getAddr = (data: any) => {
	let mapIndex = {};
	let map = {};
	let root = [];
	for (let i = 0; i < data.length; i++) {
		//
		let item = data[i];
		let pcode = item.parentPlaceCode;
		if (mapIndex[pcode]) {
			mapIndex[pcode].push(item.placeCode);
		} else {
			mapIndex[pcode] = [item.placeCode];
		}
		if (pcode === "0") {
			root.push(item.placeCode);
		}
		map[item.placeCode] = item;
	}
	//生成树
	function getTree(list, path) {
		let data = [];
		for (let i = 0; i < list.length; i++) {
			let code = list[i];
			let item = map[code];
			let _data = {
				label: item.placeName,
				value: item.placeCode,
			};
			let _path = [_data];
			if (path) {
				_path = [];
				_data.path = path;
				_path = [...path, _data];
			}
			if (mapIndex[code] && mapIndex[code].length > 0) {
				_data.children = getTree(mapIndex[code], _path);
			}
			data.push(_data);
		}
		return data;
	}
	return getTree(root);
};
export const handleCopyLinkUrl = () => {
	let textUrl = "";
	if (import.meta.env.MODE == "test") {
		textUrl = `https://yyds-test.fuyouzhao.com`;
	}
	if (import.meta.env.MODE == "uat") {
		textUrl = `http://yyds-uat.fuyouzhao.com`;
	}
	if (import.meta.env.MODE == "production") {
		textUrl = `https://yyds.fuyouzhao.com`;
	}
	return textUrl;
};

export const handleObject = () => {
	let textUrl = "";
	if (import.meta.env.MODE == "test") {
		textUrl = `https://oss-test-endpoint.fuyouzhao.com/fyz-test`;
	}
	if (import.meta.env.MODE == "uat") {
		textUrl = `https://oss-test-endpoint.fuyouzhao.com/fyz-test`;
	}
	if (import.meta.env.MODE == "production") {
		textUrl = `https://oss.fuyouzhao.com/fyz/`;
	}
	return textUrl;
};
let flag = true;
export function matchMenus(to, list, userName) {
	flag = true;
	list.forEach(item =>{
		// 有权限匹配规则
		if(item.auth){
			to.matched.forEach(match => {
				console.log('match1', match, item, userName)
				if(match.path.startsWith(item.path) && !item.owner.includes(userName)){
					flag = false
					console.log('match2', match, item, userName, flag)
					return flag
				}
			})
			
			
		}else{
			if( item.children?.length > 0  ){
				// 没有权限匹配规则
				matchMenus(to, item.children, userName)
			}
			
		}
		
	})
	console.log('flag4', flag)
	return flag
	
}
export function frontExportExcel( data: any[], SheetNumber: string, fileName: string) {
	// 创建 Workbook 对象
	const workbook = XLSX.utils.book_new();
	const worksheet = XLSX.utils.aoa_to_sheet(data);
	// 将 Worksheet 添加到 Workbook
	XLSX.utils.book_append_sheet(workbook, worksheet, SheetNumber);
	// 将 Workbook 转换为 Blob 对象
	const wbout = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
	const blob = new Blob([wbout], { type: 'application/octet-stream' });

	// 下载 Excel 文件
	saveAs(blob, fileName);

}