import { request } from "../request";

//查询模块列表分页(banner\feeds\标签)公用
export const fetchModulesList = async (params: any) => {
	const data = await request.post("/module/listByPage", params)
	return data;
}
//创建模块(标签\banner\deeds)
export const fetchModuleLabelNewBuilt = async (params: any) => {
	const data = await request.post("/module/add", params)
	return data;
}
//查询模块详情(标签\banner\deeds)
export const fetchModuleDetailById = async (params: any) => {	
	const data = await request.post("/module/detail", params)
	return data;
}
//修改模块(标签\banner\deeds)
export const fetchModuleLabelEdit = async (params: any) => {
	const data = await request.post("/module/edit", params)
	return data;
}
//标签模块的配置列表
export const fetchModuleLabelSetList = async (params: any) => {
	const data = await request.post("/module-label/listByPage", params)
	return data;
}
//查询标签列表
export const fetchModuleLabelLabList = async (params: any) => {
	const data = await request.post("/module-label/labelListByPage", params)
	return data;
}
//增加标签模块
export const fetchAddLabelToModule = async (params: any) => {
	const data = await request.post("/module-label/add", params)
	return data;
}
//删除标签模板中的标签（批量+单个）
export const fetchDelLabelModule = async (params: any) => {
	const data = await request.post("/module-label/deleteBatch", params)
	return data;
}
//标签模块同步前端
export const fetchPublishLabPage = async (params: any) => {
	const data = await request.post("/module-label/syncFrontEnd", params)
	return data;
}
//已关联商城列表分页
export const fetchLabelModuleAssociatedMall = async (params: any) => {
	const data = await request.post("/module/mallAssociatedListByPage", params)
	return data;
}
//未关联商城列表分页
export const fetchLabelModuleNotAssociatedMall = async (params: any) => {
	const data = await request.post("/module/mallUnassociatedListByPage", params)
	return data;
}
//模块添加商城关联(标签\banner\feeds)
export const fetchModuleAddAssociatedMall = async (params: any) => {
	const data = await request.post("/module/addMallRelation", params)
	return data;
}
//模块删除商城关联
export const fetchModuleDelAssociatedMall = async (params: any) => {
	const data = await request.post("/module/deleteBatch", params)
	return data;
}
//banner模块的图片分页列表
export const fetchBannerModuleImgList = async (params: any) => {
	const data = await request.post("/module-banner/listByPage", params)
	return data;
}
//添加图片到banner模块
export const fetchBannerModuleAddImg = async (params: any) => {
	const data = await request.post("/module-banner/add", params)
	return data;
}
//批量删除banner模块图片
export const fetchBannerModuleDelImgs = async (params: any) => {
	const data = await request.post("/module-banner/deleteBatch", params)
	return data;
}
//banner模块同步前端
export const fetchBannerModuleSyncFrontEnd = async (params: any) => {
	const data = await request.post("/module-banner/syncFrontEnd", params)
	return data;
}
//feeds模块已关联的spu分页列表
export const fetchFeedsModuleAssociatedSpuList = async (params: any) => {
	const data = await request.post("/module-feeds/associatedListByPage", params)
	return data;
}
//feeds模块已关联的spu分页列表
export const fetchFeedsModuleUnassociatedSpuList = async (params: any) => {
	const data = await request.post("/module-feeds/unassociatedListByPage", params)
	return data;
}
//feeds模块添加spu
export const fetchFeedsModuleAddSpu = async (params: any) => {
	const data = await request.post("/module-feeds/add", params)
	return data;
}
//(批量)删除feeds模块spu
export const fetchFeedsModuleDelSpu = async (params: any) => {
	const data = await request.post("/module-feeds/deleteBatch", params)
	return data
}
//编辑banner模块下的图片
export const fetchBannerModuleEditImg = async (params: any) => {
	const data = await request.post("/module-banner/edit", params)
	return data
}
//批量复制标签模块
export const fetchCloneLabelModule = async (params: any) => {
	const data = await request.post("/module/copy", params)
	return data
}
//标签模块重置推送
export const fetchMallLabelModulePush = async (params: any) => {
	const data = await request.post("/module-label/resetPush", params)
	return data
}
//banner模块重置推送
export const fetchMallBannerModulePush = async (params: any) => {
	const data = await request.post("/module-banner/resetPush", params)
	return data
}
//feeds模块重置推送
export const fetchMallFeedsModulePush = async (params: any) => {
	const data = await request.post("/module-feeds/resetPush", params)
	return data
}
//feeds排序
export const fetchEditFeedsItemSeq = async (params: any) => {
	const data = await request.post("/module-feeds/editFeedsItemSeq", params)
	return data
}
// 标签权重排序
export const batchSaveWeightLabels = async (params: any) => {
	const data = await request.post("/item-label/batchSaveWeight", params)
	return data
}
// Feeds模块-商品管理
export const batchSaveWeightFeedsGoods = async (params: any) => {
	const data = await request.post("/module-feeds/batchSaveWeight", params)
	return data
}
//标签复制
export const labelCoptFetch = async (params: any) => {
	const data = await request.post("/item-label/copy", params)
	return data
}