import { createApp } from "vue";
import App from "./App.vue";
import AppLoading from "./components/common/app-loading.vue";
import { setupDirectives } from "./directives";
import { setupRouter } from "./router";
import { setupAssets } from "./plugins";
import { setupStore } from "./store";
import { handleObject, localStg } from "@/utils";
import axios from "axios";

async function setupApp() {
	// import assets: js、css
	setupAssets();

	// initMenusGoods();

	// app loading
	const appLoading = createApp(AppLoading);

	appLoading.mount("#appLoading");

	const app = createApp(App);

	// store plugin: pinia
	setupStore(app);

	// vue custom directives
	setupDirectives(app);

	// vue router
	await setupRouter(app);

	appLoading.unmount();

	// mount app
	app.mount("#app");
}
async function initMenusGoods() {
	console.log("handleObject2", handleObject());
	axios
		.get(handleObject() + "/front/mis-config.json")
		.then((res) => {
			console.log("handleObject1", res);
			if (res?.data?.list?.length > 0) {
				localStg.set("menus-goods", res.data);
			}
		})
		.catch((err) => {
			localStg.set("menus-goods", []);
			console.log("err", err);
		});
}
setupApp();
