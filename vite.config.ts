import { defineConfig, loadEnv } from "vite";
import {
	createViteProxy,
	getRootPath,
	getSrcPath,
	setupVitePlugins,
	viteDefine,
} from "./build";
import { getServiceEnvConfig } from "./.env-config";

export default defineConfig((configEnv) => {
	const viteEnv = loadEnv(
		configEnv.mode,
		process.cwd()
	) as unknown as ImportMetaEnv;

	const rootPath = getRootPath();
	const srcPath = getSrcPath();

	const isOpenProxy = viteEnv.VITE_HTTP_PROXY === "Y";
	const envConfig = getServiceEnvConfig(viteEnv);
	console.log(envConfig);
	return {
		base: viteEnv.VITE_BASE_URL,
		resolve: {
			alias: {
				"~": rootPath,
				"@": srcPath,
			},
		},
		define: viteDefine,
		plugins: setupVitePlugins(viteEnv),
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `@use "./src/styles/scss/global.scss" as *;`,
				},
			},
		},
		server: {
			host: "localhost",
			port: 3200,
			https: ["prod"].includes(viteEnv.VITE_SERVICE_ENV),
			// open: true,
			proxy: createViteProxy(isOpenProxy, envConfig),
		},
		optimizeDeps: {
			include: [
				"@antv/data-set",
				"@antv/g2",
				"@better-scroll/core",
				"echarts",
				"swiper",
				"swiper/vue",
				"vditor",
				"@wangeditor/editor",
				"@wangeditor/editor-for-vue",
				"xgplayer",
			],
		},
		build: {
			reportCompressedSize: false,
			sourcemap: false,
			commonjsOptions: {
				ignoreTryCatch: false,
			},
			rollupOptions: {
				output: {
					manualChunks(id) {
						if (id.includes("node_modules")) {
							const arr = id.toString().split("node_modules/")[1].split("/");
							// console.log(JSON.stringify(arr), "arr stringify ......");
							switch (arr[0]) {
								case "@vue":
								case "axios":
								case "vant":
								case "jquery":
								case "vuex":
								case "vue-router":
								case "lib-flexible":
								case "qs":
								case "element-plus":
									return arr[0];
									break;
								default:
									return "vendor";
									break;
							}
						}
					},
					chunkFileNames: "static/chunk/[name]-[hash].js",
					entryFileNames: "static/entry/[name]-[hash].js",
					assetFileNames: "static/[ext]/[name]-[hash].[ext]",
				},
			},
		},
	};
});
